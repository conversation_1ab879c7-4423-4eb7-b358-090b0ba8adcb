using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Dtos;
using Microsoft.Extensions.Logging;
using Rlx.Shared.Models.Cos;
using Rlx.Shared.Models.Dtos;

namespace AcademicPerformance.Managers
{
    /// <summary>
    /// Personel yetkinlik değerlendirme yönetimi manager'ı
    /// </summary>
    public class StaffCompetencyManager : IStaffCompetencyManager
    {
        private readonly IStaffCompetencyStore _staffCompetencyStore;
        private readonly ILogger<StaffCompetencyManager> _logger;

        public StaffCompetencyManager(
            IStaffCompetencyStore staffCompetencyStore,
            ILogger<StaffCompetencyManager> logger)
        {
            _staffCompetencyStore = staffCompetencyStore ?? throw new ArgumentNullException(nameof(staffCompetencyStore));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region CRUD Operations

        /// <summary>
        /// Personel yetkinlik değerlendirmesi oluştur
        /// </summary>
        public async Task<StaffCompetencyDto> CreateStaffCompetencyAsync(
            StaffCompetencyCreateDto dto,
            string createdByUserId)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik değerlendirmesi oluşturuluyor - Staff: {StaffId}, Period: {Period}",
                    dto.StaffId, dto.EvaluationPeriod);

                // Validation
                var validationResult = await ValidateStaffCompetencyDataAsync(dto);
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"Validation failed: {string.Join(", ", validationResult.Errors)}");
                }

                // Check for duplicate evaluation
                var isDuplicate = await _staffCompetencyStore.CheckDuplicateEvaluationAsync(
                    dto.StaffId, dto.EvaluationPeriod, dto.EvaluatorId ?? createdByUserId);
                if (isDuplicate)
                {
                    throw new InvalidOperationException("Bu personel için bu dönemde zaten bir değerlendirme mevcut");
                }

                // Create entity
                var entity = MapCreateDtoToEntity(dto, createdByUserId);

                // Save to database
                var createdEntity = await _staffCompetencyStore.CreateStaffCompetencyAsync(entity);

                _logger.LogInformation("Personel yetkinlik değerlendirmesi başarıyla oluşturuldu - ID: {Id}", createdEntity.Id);

                return MapEntityToDto(createdEntity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi oluşturulurken hata - Staff: {StaffId}", dto.StaffId);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik değerlendirmesini güncelle
        /// </summary>
        public async Task<bool> UpdateStaffCompetencyAsync(
            StaffCompetencyUpdateDto dto,
            string updatedByUserId)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik değerlendirmesi güncelleniyor - ID: {Id}", dto.Id);

                // Get existing entity
                var existingEntity = await _staffCompetencyStore.GetStaffCompetencyAsync(dto.Id);
                if (existingEntity == null)
                {
                    return false;
                }

                // Update entity properties
                UpdateEntityFromDto(existingEntity, dto, updatedByUserId);

                // Save changes
                var result = await _staffCompetencyStore.UpdateStaffCompetencyAsync(existingEntity);

                if (result)
                {
                    _logger.LogInformation("Personel yetkinlik değerlendirmesi başarıyla güncellendi - ID: {Id}", dto.Id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi güncellenirken hata - ID: {Id}", dto.Id);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik değerlendirmesini sil
        /// </summary>
        public async Task<bool> DeleteStaffCompetencyAsync(string id, string deletedByUserId)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik değerlendirmesi siliniyor - ID: {Id}", id);

                var result = await _staffCompetencyStore.DeleteStaffCompetencyAsync(id, deletedByUserId);

                if (result)
                {
                    _logger.LogInformation("Personel yetkinlik değerlendirmesi başarıyla silindi - ID: {Id}", id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi silinirken hata - ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik değerlendirmesini getir
        /// </summary>
        public async Task<StaffCompetencyDto?> GetStaffCompetencyAsync(string id)
        {
            try
            {
                var entity = await _staffCompetencyStore.GetStaffCompetencyAsync(id);
                return entity != null ? MapEntityToDto(entity) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmesi getirilirken hata - ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik değerlendirmelerini filtreli listele
        /// </summary>
        public async Task<PagedListDto<StaffCompetencyDto>> GetStaffCompetenciesAsync(
            PagedListCo<StaffCompetencyFilterDto> co)
        {
            try
            {
                var pagedEntities = await _staffCompetencyStore.GetStaffCompetenciesAsync(co);

                var dtos = pagedEntities.Data.Select(MapEntityToDto).ToList();

                return new PagedListDto<StaffCompetencyDto>
                {
                    Data = dtos,
                    TotalCount = pagedEntities.TotalCount,
                    Page = pagedEntities.Page,
                    Size = pagedEntities.Size,
                    Count = dtos.Count()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik değerlendirmeleri listelenirken hata");
                throw;
            }
        }

        #endregion

        #region Dashboard Operations

        /// <summary>
        /// Personel yetkinlik dashboard'ını getir
        /// </summary>
        public async Task<StaffCompetencyDashboardDto> GetStaffCompetencyDashboardAsync(
            string departmentId,
            string period)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik dashboard'ı getiriliyor - Department: {DepartmentId}, Period: {Period}",
                    departmentId, period);

                var dashboard = new StaffCompetencyDashboardDto
                {
                    DepartmentId = departmentId,
                    Period = period,
                    LastUpdated = DateTime.UtcNow
                };

                // Get department evaluations
                var evaluations = await _staffCompetencyStore.GetStaffCompetenciesByDepartmentAsync(departmentId, period);

                // Calculate statistics
                dashboard.TotalStaffCount = await GetDepartmentStaffCountAsync(departmentId);
                dashboard.EvaluatedStaffCount = evaluations.Count;
                dashboard.PendingEvaluationCount = dashboard.TotalStaffCount - dashboard.EvaluatedStaffCount;
                dashboard.EvaluationCompletionRate = dashboard.TotalStaffCount > 0
                    ? (double)dashboard.EvaluatedStaffCount / dashboard.TotalStaffCount * 100
                    : 0;

                if (evaluations.Any())
                {
                    // Calculate average scores from ratings
                    var allScores = evaluations.Select(e => CalculateCompetencyScoresFromRatings(e.CompetencyRatings)).ToList();

                    dashboard.AverageOverallScore = allScores.Where(s => s.ContainsKey("Overall")).Average(s => s["Overall"]);
                    dashboard.AverageTeachingScore = allScores.Where(s => s.ContainsKey("Teaching")).Average(s => s["Teaching"]);
                    dashboard.AverageResearchScore = allScores.Where(s => s.ContainsKey("Research")).Average(s => s["Research"]);
                    dashboard.AverageServiceScore = allScores.Where(s => s.ContainsKey("Service")).Average(s => s["Service"]);
                    dashboard.AverageLeadershipScore = allScores.Where(s => s.ContainsKey("Leadership")).Average(s => s["Leadership"]);

                    // Calculate performance distribution
                    var overallScores = allScores.Where(s => s.ContainsKey("Overall")).Select(s => s["Overall"]).ToList();
                    dashboard.ExcellentPerformers = overallScores.Count(s => s >= 90);
                    dashboard.GoodPerformers = overallScores.Count(s => s >= 70 && s < 90);
                    dashboard.AveragePerformers = overallScores.Count(s => s >= 50 && s < 70);
                    dashboard.BelowAveragePerformers = overallScores.Count(s => s < 50);

                    // Get trend data
                    dashboard.TrendData = await GetCompetencyTrendAnalysisAsync(departmentId, 6);

                    // Get top and improvement areas
                    dashboard.TopPerformanceAreas = await GetTopPerformanceAreasAsync(departmentId, period);
                    dashboard.ImprovementNeededAreas = await GetImprovementNeededAreasAsync(departmentId, period);
                }

                return dashboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik dashboard'ı getirilirken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik istatistiklerini getir
        /// </summary>
        public async Task<List<CompetencyAreaSummaryDto>> GetCompetencyStatisticsAsync(
            string departmentId,
            string period)
        {
            try
            {
                return await _staffCompetencyStore.CalculateDepartmentCompetencyStatisticsAsync(departmentId, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik istatistikleri getirilirken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik trend analizini getir
        /// </summary>
        public async Task<List<CompetencyTrendDataDto>> GetCompetencyTrendAnalysisAsync(
            string staffId,
            int periodCount = 12)
        {
            try
            {
                return await _staffCompetencyStore.GetStaffCompetencyTrendDataAsync(staffId, periodCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik trend analizi getirilirken hata - Staff: {StaffId}", staffId);
                throw;
            }
        }

        #endregion

        #region Comparison & Analysis

        /// <summary>
        /// Personel yetkinlik karşılaştırması yap
        /// </summary>
        public async Task<StaffCompetencyComparisonDto> CompareStaffCompetenciesAsync(
            List<string> staffIds,
            string period)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik karşılaştırması yapılıyor - Staff Count: {Count}, Period: {Period}",
                    staffIds.Count, period);

                var comparison = new StaffCompetencyComparisonDto
                {
                    StaffIds = staffIds,
                    ComparisonPeriod = period,
                    GeneratedAt = DateTime.UtcNow
                };

                var comparisonData = new List<StaffCompetencyComparisonItemDto>();

                foreach (var staffId in staffIds)
                {
                    var evaluations = await _staffCompetencyStore.GetStaffCompetenciesByStaffIdAsync(staffId, period, 1);
                    var latestEvaluation = evaluations.FirstOrDefault();

                    if (latestEvaluation != null)
                    {
                        var competencyScores = CalculateCompetencyScoresFromRatings(latestEvaluation.CompetencyRatings);
                        comparisonData.Add(new StaffCompetencyComparisonItemDto
                        {
                            StaffId = staffId,
                            StaffName = latestEvaluation.AcademicianUniveristyUserId,
                            OverallScore = competencyScores.Values.Any() ? competencyScores.Values.Average() : 0.0,
                            CompetencyScores = competencyScores,
                            EvaluationDate = latestEvaluation.SubmittedAt,
                            Ranking = 0 // Sonra hesaplanacak
                        });
                    }
                }

                // Calculate rankings
                var rankedData = comparisonData.OrderByDescending(x => x.OverallScore).ToList();
                for (int i = 0; i < rankedData.Count; i++)
                {
                    rankedData[i].Ranking = i + 1;
                }

                comparison.ComparisonData = rankedData;

                // Get benchmark data
                if (comparisonData.Any())
                {
                    var firstStaff = comparisonData.First();
                    var departmentId = await GetStaffDepartmentIdAsync(firstStaff.StaffId);
                    if (!string.IsNullOrEmpty(departmentId))
                    {
                        comparison.Benchmark = await CalculateCompetencyBenchmarkAsync(departmentId, period);
                    }
                }

                return comparison;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik karşılaştırması yapılırken hata");
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik analizi yap
        /// </summary>
        public async Task<StaffCompetencyAnalysisDto> AnalyzeStaffCompetencyAsync(
            string analysisType,
            string targetId,
            string period,
            string analyzedByUserId)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik analizi yapılıyor - Type: {Type}, Target: {TargetId}",
                    analysisType, targetId);

                var analysis = new StaffCompetencyAnalysisDto
                {
                    AnalysisType = analysisType,
                    TargetId = targetId,
                    Period = period,
                    AnalysisDate = DateTime.UtcNow,
                    AnalyzedByUserId = analyzedByUserId,
                    AnalyzedByUserName = await GetUserNameAsync(analyzedByUserId)
                };

                List<StaffCompetencyEvaluationEntity> evaluations;

                switch (analysisType.ToLower())
                {
                    case "individual":
                        evaluations = await _staffCompetencyStore.GetStaffCompetenciesByStaffIdAsync(targetId, period);
                        break;
                    case "department":
                        evaluations = await _staffCompetencyStore.GetStaffCompetenciesByDepartmentAsync(targetId, period);
                        break;
                    default:
                        throw new ArgumentException($"Unsupported analysis type: {analysisType}");
                }

                if (evaluations.Any())
                {
                    analysis.AnalysisResult = PerformCompetencyAnalysis(evaluations);
                    analysis.GapAnalysis = PerformGapAnalysis(evaluations);
                    analysis.Recommendations = GenerateRecommendations(analysis.AnalysisResult, analysis.GapAnalysis);
                }

                return analysis;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik analizi yapılırken hata - Type: {Type}, Target: {TargetId}",
                    analysisType, targetId);
                throw;
            }
        }

        /// <summary>
        /// Yetkinlik benchmark'ını hesapla
        /// </summary>
        public async Task<CompetencyBenchmarkDto> CalculateCompetencyBenchmarkAsync(
            string departmentId,
            string period)
        {
            try
            {
                return await _staffCompetencyStore.CalculateCompetencyBenchmarkAsync(departmentId, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik benchmark'ı hesaplanırken hata - Department: {DepartmentId}", departmentId);
                throw;
            }
        }

        #endregion

        #region Evaluation Form Operations

        /// <summary>
        /// Yetkinlik değerlendirme formu oluştur
        /// </summary>
        public async Task<CompetencyEvaluationFormDto> CreateEvaluationFormAsync(
            CompetencyEvaluationFormCreateDto dto,
            string createdByUserId)
        {
            try
            {
                _logger.LogInformation("Yetkinlik değerlendirme formu oluşturuluyor - Staff: {StaffId}, Evaluator: {EvaluatorId}",
                    dto.StaffId, dto.EvaluatorId);

                // Validation
                var validationResult = await ValidateEvaluationFormAsync(dto);
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"Form validation failed: {string.Join(", ", validationResult.Errors)}");
                }

                // Create form entity and save
                var formEntity = MapFormCreateDtoToEntity(dto, createdByUserId);

                // For now, return a basic form DTO
                // In a real implementation, this would save to a forms table
                var formDto = new CompetencyEvaluationFormDto
                {
                    Id = Guid.NewGuid().ToString(),
                    StaffId = dto.StaffId,
                    StaffName = await GetStaffNameAsync(dto.StaffId),
                    EvaluatorId = dto.EvaluatorId,
                    EvaluatorName = await GetUserNameAsync(dto.EvaluatorId),
                    EvaluationType = dto.EvaluationType,
                    Period = dto.Period,
                    EvaluationDate = dto.EvaluationDate,
                    Categories = dto.Categories.Select(MapCategoryCreateDtoToDto).ToList(),
                    Status = "Draft",
                    GeneralComments = dto.GeneralComments ?? "",
                    StrengthAreas = dto.StrengthAreas ?? new List<string>(),
                    ImprovementAreas = dto.ImprovementAreas ?? new List<string>()
                };

                return formDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik değerlendirme formu oluşturulurken hata - Staff: {StaffId}", dto.StaffId);
                throw;
            }
        }

        /// <summary>
        /// Yetkinlik değerlendirme formunu getir
        /// </summary>
        public async Task<CompetencyEvaluationFormDto?> GetEvaluationFormAsync(string formId)
        {
            try
            {
                // This would typically fetch from a forms table
                // For now, return null as placeholder
                return await Task.FromResult<CompetencyEvaluationFormDto?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik değerlendirme formu getirilirken hata - Form: {FormId}", formId);
                throw;
            }
        }

        /// <summary>
        /// Personel için değerlendirme formlarını getir
        /// </summary>
        public async Task<List<CompetencyEvaluationFormDto>> GetEvaluationFormsForStaffAsync(
            string staffId,
            string period)
        {
            try
            {
                // This would typically fetch from a forms table
                // For now, return empty list as placeholder
                return await Task.FromResult(new List<CompetencyEvaluationFormDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel değerlendirme formları getirilirken hata - Staff: {StaffId}", staffId);
                throw;
            }
        }

        /// <summary>
        /// Değerlendirme formunu onayla
        /// </summary>
        public async Task<bool> ApproveEvaluationFormAsync(string formId, string approvedByUserId)
        {
            try
            {
                _logger.LogInformation("Değerlendirme formu onaylanıyor - Form: {FormId}, Approver: {ApproverId}",
                    formId, approvedByUserId);

                // This would typically update the form status in database
                // For now, return true as placeholder
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Değerlendirme formu onaylanırken hata - Form: {FormId}", formId);
                throw;
            }
        }

        #endregion

        #region Report Operations

        /// <summary>
        /// Personel yetkinlik raporu oluştur
        /// </summary>
        public async Task<StaffCompetencyReportDto> GenerateCompetencyReportAsync(
            string reportType,
            string scopeId,
            string period,
            string generatedByUserId)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik raporu oluşturuluyor - Type: {Type}, Scope: {ScopeId}",
                    reportType, scopeId);

                var report = new StaffCompetencyReportDto
                {
                    ReportId = Guid.NewGuid().ToString(),
                    ReportType = reportType,
                    ReportTitle = $"Personel Yetkinlik Raporu - {reportType}",
                    Period = period,
                    ReportDate = DateTime.UtcNow,
                    ScopeId = scopeId,
                    ScopeName = await GetScopeNameAsync(reportType, scopeId),
                    GeneratedByUserId = generatedByUserId,
                    GeneratedByUserName = await GetUserNameAsync(generatedByUserId),
                    ReportFormat = "HTML"
                };

                // Generate report content based on type
                switch (reportType.ToLower())
                {
                    case "individual":
                        await GenerateIndividualReportAsync(report, scopeId, period);
                        break;
                    case "department":
                        await GenerateDepartmentReportAsync(report, scopeId, period);
                        break;
                    case "faculty":
                        await GenerateFacultyReportAsync(report, scopeId, period);
                        break;
                    default:
                        throw new ArgumentException($"Unsupported report type: {reportType}");
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik raporu oluşturulurken hata - Type: {Type}, Scope: {ScopeId}",
                    reportType, scopeId);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik raporunu export et
        /// </summary>
        public async Task<FileExportResultDto> ExportCompetencyReportAsync(string reportId, string format)
        {
            try
            {
                _logger.LogInformation("Personel yetkinlik raporu export ediliyor - Report: {ReportId}, Format: {Format}",
                    reportId, format);

                // This would typically generate and save the report file
                // For now, return a placeholder result
                return await Task.FromResult(new FileExportResultDto
                {
                    FileName = $"competency_report_{reportId}.{format.ToLower()}",
                    FilePath = $"/reports/competency/{reportId}.{format.ToLower()}",
                    FileSize = 1024,
                    ContentType = GetContentType(format),
                    CreatedAt = DateTime.UtcNow,
                    DownloadUrl = $"/api/reports/download/{reportId}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik raporu export edilirken hata - Report: {ReportId}", reportId);
                throw;
            }
        }

        #endregion

        #region Calculation Operations

        /// <summary>
        /// Personel genel yetkinlik skorunu hesapla
        /// </summary>
        public async Task<double> CalculateOverallCompetencyScoreAsync(string staffId, string period)
        {
            try
            {
                return await _staffCompetencyStore.CalculateOverallCompetencyScoreAsync(staffId, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel genel yetkinlik skoru hesaplanırken hata - Staff: {StaffId}", staffId);
                throw;
            }
        }

        /// <summary>
        /// Yetkinlik alanı skorunu hesapla
        /// </summary>
        public async Task<double> CalculateCompetencyAreaScoreAsync(
            string staffId,
            string competencyArea,
            string period)
        {
            try
            {
                return await _staffCompetencyStore.CalculateCompetencyAreaScoreAsync(staffId, competencyArea, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik alanı skoru hesaplanırken hata - Staff: {StaffId}, Area: {Area}",
                    staffId, competencyArea);
                throw;
            }
        }

        /// <summary>
        /// Yetkinlik büyüme oranını hesapla
        /// </summary>
        public async Task<double> CalculateCompetencyGrowthRateAsync(
            string staffId,
            string currentPeriod,
            string previousPeriod)
        {
            try
            {
                return await _staffCompetencyStore.CalculateCompetencyGrowthRateAsync(staffId, currentPeriod, previousPeriod);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Yetkinlik büyüme oranı hesaplanırken hata - Staff: {StaffId}", staffId);
                throw;
            }
        }

        #endregion

        #region Validation Operations

        /// <summary>
        /// Personel yetkinlik verilerini doğrula
        /// </summary>
        public async Task<ValidationResultDto> ValidateStaffCompetencyDataAsync(StaffCompetencyCreateDto dto)
        {
            var result = new ValidationResultDto { IsValid = true, Errors = new List<string>() };

            try
            {
                // Basic validation
                if (string.IsNullOrEmpty(dto.StaffId))
                    result.Errors.Add("Personel ID gereklidir");

                if (string.IsNullOrEmpty(dto.EvaluationPeriod))
                    result.Errors.Add("Değerlendirme dönemi gereklidir");

                if (dto.EvaluationDate == default)
                    result.Errors.Add("Değerlendirme tarihi gereklidir");

                // Score validation (0-100 range)
                var scores = new Dictionary<string, double>
                {
                    ["Öğretim Yetkinliği"] = dto.TeachingCompetency,
                    ["Araştırma Yetkinliği"] = dto.ResearchCompetency,
                    ["Hizmet Yetkinliği"] = dto.ServiceCompetency,
                    ["Liderlik Yetkinliği"] = dto.LeadershipCompetency,
                    ["İletişim Yetkinliği"] = dto.CommunicationCompetency,
                    ["Teknik Yetkinlik"] = dto.TechnicalCompetency,
                    ["İnovasyon Yetkinliği"] = dto.InnovationCompetency,
                    ["İşbirliği Yetkinliği"] = dto.CollaborationCompetency
                };

                foreach (var score in scores)
                {
                    if (score.Value < 0 || score.Value > 100)
                        result.Errors.Add($"{score.Key} 0-100 arasında olmalıdır");
                }

                // Performance indicators validation
                if (dto.StudentEvaluationScore < 0 || dto.StudentEvaluationScore > 100)
                    result.Errors.Add("Öğrenci değerlendirme skoru 0-100 arasında olmalıdır");

                if (dto.PeerEvaluationScore < 0 || dto.PeerEvaluationScore > 100)
                    result.Errors.Add("Meslektaş değerlendirme skoru 0-100 arasında olmalıdır");

                if (dto.SelfEvaluationScore < 0 || dto.SelfEvaluationScore > 100)
                    result.Errors.Add("Öz değerlendirme skoru 0-100 arasında olmalıdır");

                if (dto.SupervisorEvaluationScore < 0 || dto.SupervisorEvaluationScore > 100)
                    result.Errors.Add("Yönetici değerlendirme skoru 0-100 arasında olmalıdır");

                result.IsValid = !result.Errors.Any();

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik verisi doğrulanırken hata");
                result.IsValid = false;
                result.Errors.Add("Doğrulama sırasında hata oluştu");
                return result;
            }
        }

        /// <summary>
        /// Değerlendirme formunu doğrula
        /// </summary>
        public async Task<ValidationResultDto> ValidateEvaluationFormAsync(CompetencyEvaluationFormCreateDto dto)
        {
            var result = new ValidationResultDto { IsValid = true, Errors = new List<string>() };

            try
            {
                if (string.IsNullOrEmpty(dto.StaffId))
                    result.Errors.Add("Personel ID gereklidir");

                if (string.IsNullOrEmpty(dto.EvaluatorId))
                    result.Errors.Add("Değerlendirici ID gereklidir");

                if (string.IsNullOrEmpty(dto.EvaluationType))
                    result.Errors.Add("Değerlendirme türü gereklidir");

                if (string.IsNullOrEmpty(dto.Period))
                    result.Errors.Add("Dönem gereklidir");

                if (dto.EvaluationDate == default)
                    result.Errors.Add("Değerlendirme tarihi gereklidir");

                if (dto.Categories == null || !dto.Categories.Any())
                    result.Errors.Add("En az bir kategori gereklidir");

                result.IsValid = !result.Errors.Any();

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Değerlendirme formu doğrulanırken hata");
                result.IsValid = false;
                result.Errors.Add("Form doğrulama sırasında hata oluştu");
                return result;
            }
        }

        #endregion

        #region Utility Operations

        /// <summary>
        /// Personel yetkinlik verilerini senkronize et
        /// </summary>
        public async Task<bool> SynchronizeStaffCompetencyDataAsync(string staffId, string period)
        {
            try
            {
                return await _staffCompetencyStore.SynchronizeStaffCompetencyDataAsync(staffId, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik verileri senkronize edilirken hata - Staff: {StaffId}", staffId);
                throw;
            }
        }

        /// <summary>
        /// Personel yetkinlik cache'ini temizle
        /// </summary>
        public async Task<bool> ClearStaffCompetencyCacheAsync(string staffId)
        {
            try
            {
                // This would typically clear cache entries
                // For now, return true as placeholder
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Personel yetkinlik cache'i temizlenirken hata - Staff: {StaffId}", staffId);
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// CreateDto'yu Entity'ye map et
        /// </summary>
        private StaffCompetencyEvaluationEntity MapCreateDtoToEntity(StaffCompetencyCreateDto dto, string createdByUserId)
        {
            return new StaffCompetencyEvaluationEntity
            {
                Id = Guid.NewGuid().ToString(),
                StaffId = dto.StaffId, // Required property eklendi
                CompetencyId = "general-competency", // Required property - default değer
                AcademicianUniveristyUserId = dto.StaffId,
                EvaluatingManagerUserId = dto.EvaluatorId ?? createdByUserId,
                EvaluationContextId = dto.EvaluationPeriod,
                OverallComments = dto.Comments,
                SubmittedAt = dto.EvaluationDate,
                Disabled = false,
                Deleted = false
            };
        }

        /// <summary>
        /// Entity'yi DTO'ya map et
        /// </summary>
        private StaffCompetencyDto MapEntityToDto(StaffCompetencyEvaluationEntity entity)
        {
            // Calculate competency scores from ratings
            var competencyScores = CalculateCompetencyScoresFromRatings(entity.CompetencyRatings);

            return new StaffCompetencyDto
            {
                Id = entity.Id,
                StaffId = entity.AcademicianUniveristyUserId,
                StaffName = "", // Bu bilgi başka bir servisten alınacak
                StaffEmail = "",
                DepartmentId = "",
                DepartmentName = "",
                Position = "",
                AcademicTitle = "",
                EvaluationPeriod = entity.EvaluationContextId,
                EvaluationDate = entity.SubmittedAt,
                OverallCompetencyScore = competencyScores.ContainsKey("Overall") ? competencyScores["Overall"] : 0,
                TeachingCompetency = competencyScores.ContainsKey("Teaching") ? competencyScores["Teaching"] : 0,
                ResearchCompetency = competencyScores.ContainsKey("Research") ? competencyScores["Research"] : 0,
                ServiceCompetency = competencyScores.ContainsKey("Service") ? competencyScores["Service"] : 0,
                LeadershipCompetency = competencyScores.ContainsKey("Leadership") ? competencyScores["Leadership"] : 0,
                CommunicationCompetency = competencyScores.ContainsKey("Communication") ? competencyScores["Communication"] : 0,
                TechnicalCompetency = competencyScores.ContainsKey("Technical") ? competencyScores["Technical"] : 0,
                InnovationCompetency = competencyScores.ContainsKey("Innovation") ? competencyScores["Innovation"] : 0,
                CollaborationCompetency = competencyScores.ContainsKey("Collaboration") ? competencyScores["Collaboration"] : 0,
                PublicationCount = 0, // Bu bilgi başka bir servisten alınacak
                ProjectCount = 0,
                StudentEvaluationScore = 0,
                PeerEvaluationScore = 0,
                SelfEvaluationScore = 0,
                SupervisorEvaluationScore = 0,
                Comments = entity.OverallComments ?? "",
                EvaluationStatus = "Completed", // Default status
                EvaluatorId = entity.EvaluatingManagerUserId,
                EvaluatorName = "", // Bu bilgi başka bir servisten alınacak
                ApprovalDate = null,
                ApprovedByUserId = null,
                ApprovedByUserName = null,
                CreatedByUserId = "",
                CreatedByUserName = "",
                CreatedAt = DateTime.UtcNow,
                UpdatedByUserId = "",
                UpdatedByUserName = "",
                UpdatedAt = DateTime.UtcNow,
                IsActive = !entity.Disabled
            };
        }

        /// <summary>
        /// Entity'yi UpdateDto'dan güncelle
        /// </summary>
        private void UpdateEntityFromDto(StaffCompetencyEvaluationEntity entity, StaffCompetencyUpdateDto dto, string updatedByUserId)
        {
            entity.EvaluationContextId = dto.EvaluationPeriod;
            entity.SubmittedAt = dto.EvaluationDate;
            entity.OverallComments = dto.Comments;
            entity.EvaluatingManagerUserId = dto.EvaluatorId ?? entity.EvaluatingManagerUserId;

            // CompetencyRatings güncellenmesi ayrı bir method'da yapılacak
            // UpdateCompetencyRatings(entity, dto);
        }

        /// <summary>
        /// Genel yetkinlik skorunu hesapla
        /// </summary>
        private double CalculateOverallScore(StaffCompetencyEvaluationEntity entity)
        {
            if (entity.CompetencyRatings == null || !entity.CompetencyRatings.Any())
                return 0;

            // Rating'leri numeric değerlere çevir ve ortalama al
            var numericRatings = new List<double>();

            foreach (var rating in entity.CompetencyRatings)
            {
                if (double.TryParse(rating.Rating, out double numericValue))
                {
                    numericRatings.Add(numericValue);
                }
                else
                {
                    // String rating'leri numeric'e çevir (örn: "Excellent" = 5, "Good" = 4, vb.)
                    numericRatings.Add(ConvertRatingToNumeric(rating.Rating));
                }
            }

            return numericRatings.Any() ? numericRatings.Average() : 0;
        }

        /// <summary>
        /// Performans seviyesini belirle
        /// </summary>
        private string GetPerformanceLevel(double score)
        {
            return score switch
            {
                >= 90 => "Excellent",
                >= 70 => "Good",
                >= 50 => "Average",
                _ => "BelowAverage"
            };
        }

        /// <summary>
        /// Placeholder helper methods - gerçek implementasyonda bu method'lar implement edilecek
        /// </summary>
        private async Task<int> GetDepartmentStaffCountAsync(string departmentId) => await Task.FromResult(10);
        private async Task<List<CompetencyAreaSummaryDto>> GetTopPerformanceAreasAsync(string departmentId, string period) => await Task.FromResult(new List<CompetencyAreaSummaryDto>());
        private async Task<List<CompetencyAreaSummaryDto>> GetImprovementNeededAreasAsync(string departmentId, string period) => await Task.FromResult(new List<CompetencyAreaSummaryDto>());
        private async Task<string> GetStaffDepartmentIdAsync(string staffId) => await Task.FromResult("dept-001");
        private async Task<string> GetUserNameAsync(string userId) => await Task.FromResult("User Name");
        private async Task<string> GetStaffNameAsync(string staffId) => await Task.FromResult("Staff Name");
        private async Task<string> GetScopeNameAsync(string reportType, string scopeId) => await Task.FromResult($"{reportType} - {scopeId}");
        private string GetContentType(string format) => format.ToLower() switch { "pdf" => "application/pdf", "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", _ => "text/html" };

        private CompetencyAnalysisResultDto PerformCompetencyAnalysis(List<StaffCompetencyEvaluationEntity> evaluations) => new();
        private List<CompetencyGapAnalysisDto> PerformGapAnalysis(List<StaffCompetencyEvaluationEntity> evaluations) => new();
        private List<CompetencyRecommendationDto> GenerateRecommendations(CompetencyAnalysisResultDto analysis, List<CompetencyGapAnalysisDto> gaps) => new();
        private StaffCompetencyEvaluationEntity MapFormCreateDtoToEntity(CompetencyEvaluationFormCreateDto dto, string createdByUserId) => new()
        {
            Id = Guid.NewGuid().ToString(),
            StaffId = dto.StaffId, // Required property eklendi
            CompetencyId = dto.Period ?? "default", // Required property eklendi
            AcademicianUniveristyUserId = dto.StaffId,
            EvaluatingManagerUserId = createdByUserId,
            EvaluationContextId = dto.Period ?? "default",
            OverallComments = "",
            SubmittedAt = DateTime.UtcNow,
            Disabled = false,
            Deleted = false
        };
        private CompetencyEvaluationCategoryDto MapCategoryCreateDtoToDto(CompetencyEvaluationCategoryCreateDto dto) => new();

        private async Task GenerateIndividualReportAsync(StaffCompetencyReportDto report, string staffId, string period) => await Task.CompletedTask;
        private async Task GenerateDepartmentReportAsync(StaffCompetencyReportDto report, string departmentId, string period) => await Task.CompletedTask;
        private async Task GenerateFacultyReportAsync(StaffCompetencyReportDto report, string facultyId, string period) => await Task.CompletedTask;

        /// <summary>
        /// CompetencyRatings'lerden yetkinlik skorlarını hesapla
        /// </summary>
        private Dictionary<string, double> CalculateCompetencyScoresFromRatings(ICollection<CompetencyRatingEntity>? ratings)
        {
            var scores = new Dictionary<string, double>();

            if (ratings == null || !ratings.Any())
                return scores;

            // Rating'leri competency türlerine göre grupla ve ortalama al
            var groupedRatings = ratings.GroupBy(r => GetCompetencyTypeFromSystemId(r.CompetencySystemId));

            foreach (var group in groupedRatings)
            {
                var numericRatings = group.Select(r => ConvertRatingToNumeric(r.Rating)).ToList();
                if (numericRatings.Any())
                {
                    scores[group.Key] = numericRatings.Average();
                }
            }

            // Overall score hesapla
            if (scores.Any())
            {
                scores["Overall"] = scores.Values.Average();
            }

            return scores;
        }

        /// <summary>
        /// Rating string'ini numeric değere çevir
        /// </summary>
        private double ConvertRatingToNumeric(string rating)
        {
            return rating?.ToLower() switch
            {
                "excellent" or "5" => 5.0,
                "very good" or "4" => 4.0,
                "good" or "3" => 3.0,
                "fair" or "2" => 2.0,
                "poor" or "1" => 1.0,
                _ => double.TryParse(rating, out double value) ? value : 0.0
            };
        }

        /// <summary>
        /// CompetencySystemId'den yetkinlik türünü çıkar
        /// </summary>
        private string GetCompetencyTypeFromSystemId(string systemId)
        {
            return systemId?.ToUpper() switch
            {
                var id when id.Contains("TEACHING") => "Teaching",
                var id when id.Contains("RESEARCH") => "Research",
                var id when id.Contains("SERVICE") => "Service",
                var id when id.Contains("LEADERSHIP") => "Leadership",
                var id when id.Contains("COMMUNICATION") => "Communication",
                var id when id.Contains("TECHNICAL") => "Technical",
                var id when id.Contains("INNOVATION") => "Innovation",
                var id when id.Contains("COLLABORATION") => "Collaboration",
                _ => "Other"
            };
        }

        #endregion
    }
}
