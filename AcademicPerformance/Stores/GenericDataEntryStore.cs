using Microsoft.EntityFrameworkCore;
using AcademicPerformance.DbContexts;
using AcademicPerformance.Interfaces;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using AcademicPerformance.Models.Cos;
using Rlx.Shared.Models.Dtos;
using Rlx.Shared.Models.Cos;

namespace AcademicPerformance.Stores
{
    /// <summary>
    /// Generic Data Entry Store implementation
    /// </summary>
    public class GenericDataEntryStore : IGenericDataEntryStore
    {
        private readonly AcademicPerformanceDbContext _context;
        private readonly ILogger<GenericDataEntryStore> _logger;

        public GenericDataEntryStore(AcademicPerformanceDbContext context, ILogger<GenericDataEntryStore> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region Definition CRUD Operations

        public async Task<GenericDataEntryDefinitionEntity> CreateDefinitionAsync(GenericDataEntryDefinitionEntity entity)
        {
            try
            {
                _logger.LogInformation("Generic data entry definition oluşturuluyor: {Name}", entity.Name);

                entity.CreatedAt = DateTime.UtcNow;
                _context.GenericDataEntryDefinitions.Add(entity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Generic data entry definition başarıyla oluşturuldu: {Id}", entity.Id);
                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition oluşturma hatası: {Name}", entity.Name);
                throw;
            }
        }

        public async Task<bool> UpdateDefinitionAsync(GenericDataEntryDefinitionEntity entity)
        {
            try
            {
                _logger.LogInformation("Generic data entry definition güncelleniyor: {Id}", entity.Id);

                entity.UpdatedAt = DateTime.UtcNow;
                _context.GenericDataEntryDefinitions.Update(entity);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Generic data entry definition başarıyla güncellendi: {Id}", entity.Id);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition güncelleme hatası: {Id}", entity.Id);
                throw;
            }
        }

        public async Task<bool> DeleteDefinitionAsync(string definitionId)
        {
            try
            {
                _logger.LogInformation("Generic data entry definition siliniyor: {Id}", definitionId);

                var entity = await _context.GenericDataEntryDefinitions
                    .FirstOrDefaultAsync(d => d.Id == definitionId);

                if (entity == null)
                {
                    return false;
                }

                // Soft delete
                entity.IsActive = false;
                entity.UpdatedAt = DateTime.UtcNow;

                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Generic data entry definition başarıyla silindi: {Id}", definitionId);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition silme hatası: {Id}", definitionId);
                throw;
            }
        }

        public async Task<GenericDataEntryDefinitionEntity?> GetDefinitionByIdAsync(string definitionId)
        {
            try
            {
                return await _context.GenericDataEntryDefinitions
                    .FirstOrDefaultAsync(d => d.Id == definitionId && d.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition getirme hatası: {Id}", definitionId);
                throw;
            }
        }

        public async Task<PagedListDto<GenericDataEntryDefinitionEntity>> GetDefinitionsAsync(PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                var query = _context.GenericDataEntryDefinitions.AsQueryable();

                // Filters
                if (co.Criteria != null)
                {
                    if (co.Criteria!.ActiveOnly.HasValue && co.Criteria!.ActiveOnly.Value)
                    {
                        query = query.Where(d => d.IsActive);
                    }

                    if (!string.IsNullOrEmpty(co.Criteria!.Category))
                    {
                        query = query.Where(d => d.Category.Contains(co.Criteria!.Category));
                    }

                    if (!string.IsNullOrEmpty(co.Criteria!.SearchTerm))
                    {
                        query = query.Where(d => d.Name.Contains(co.Criteria!.SearchTerm) ||
                                               d.Description.Contains(co.Criteria!.SearchTerm) ||
                                               d.Category.Contains(co.Criteria!.SearchTerm));
                    }

                    if (co.Criteria!.StartDate.HasValue)
                    {
                        query = query.Where(d => d.CreatedAt >= co.Criteria!.StartDate.Value);
                    }

                    if (co.Criteria!.EndDate.HasValue)
                    {
                        query = query.Where(d => d.CreatedAt <= co.Criteria!.EndDate.Value);
                    }

                    if (!string.IsNullOrEmpty(co.Criteria!.CreatedByUserId))
                    {
                        query = query.Where(d => d.CreatedByUserId == co.Criteria!.CreatedByUserId);
                    }
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(d => d.CreatedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<GenericDataEntryDefinitionEntity>
                {
                    Data = items,
                    Count = totalCount,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition'ları listeleme hatası");
                throw;
            }
        }

        public async Task<GenericDataEntryDefinitionEntity?> GetDefinitionByNameAsync(string name)
        {
            try
            {
                return await _context.GenericDataEntryDefinitions
                    .FirstOrDefaultAsync(d => d.Name == name && d.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry definition name ile getirme hatası: {Name}", name);
                throw;
            }
        }

        #endregion

        #region Record CRUD Operations

        public async Task<GenericDataEntryRecordEntity> CreateRecordAsync(GenericDataEntryRecordEntity entity)
        {
            try
            {
                _logger.LogInformation("Generic data entry record oluşturuluyor: {DefinitionId}", entity.DefinitionId);

                entity.CreatedAt = DateTime.UtcNow;
                _context.GenericDataEntryRecords.Add(entity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Generic data entry record başarıyla oluşturuldu: {Id}", entity.Id);
                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record oluşturma hatası: {DefinitionId}", entity.DefinitionId);
                throw;
            }
        }

        public async Task<bool> UpdateRecordAsync(GenericDataEntryRecordEntity entity)
        {
            try
            {
                _logger.LogInformation("Generic data entry record güncelleniyor: {Id}", entity.Id);

                entity.UpdatedAt = DateTime.UtcNow;
                _context.GenericDataEntryRecords.Update(entity);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Generic data entry record başarıyla güncellendi: {Id}", entity.Id);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record güncelleme hatası: {Id}", entity.Id);
                throw;
            }
        }

        public async Task<bool> DeleteRecordAsync(string recordId)
        {
            try
            {
                _logger.LogInformation("Generic data entry record siliniyor: {Id}", recordId);

                var entity = await _context.GenericDataEntryRecords
                    .FirstOrDefaultAsync(r => r.Id == recordId);

                if (entity == null)
                {
                    return false;
                }

                // Hard delete for records
                _context.GenericDataEntryRecords.Remove(entity);
                var result = await _context.SaveChangesAsync();

                _logger.LogInformation("Generic data entry record başarıyla silindi: {Id}", recordId);
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record silme hatası: {Id}", recordId);
                throw;
            }
        }

        public async Task<GenericDataEntryRecordEntity?> GetRecordByIdAsync(string recordId)
        {
            try
            {
                return await _context.GenericDataEntryRecords
                    .FirstOrDefaultAsync(r => r.Id == recordId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record getirme hatası: {Id}", recordId);
                throw;
            }
        }

        public async Task<PagedListDto<GenericDataEntryRecordEntity>> GetRecordsAsync(PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                var query = _context.GenericDataEntryRecords.AsQueryable();

                // Filters
                if (co.Criteria != null)
                {
                    if (!string.IsNullOrEmpty(co.Criteria!.DefinitionId))
                    {
                        query = query.Where(r => r.DefinitionId == co.Criteria!.DefinitionId);
                    }

                    if (!string.IsNullOrEmpty(co.Criteria!.Status))
                    {
                        query = query.Where(r => r.Status == co.Criteria!.Status);
                    }

                    if (co.Criteria!.StartDate.HasValue)
                    {
                        query = query.Where(r => r.CreatedAt >= co.Criteria!.StartDate.Value);
                    }

                    if (co.Criteria!.EndDate.HasValue)
                    {
                        query = query.Where(r => r.CreatedAt <= co.Criteria!.EndDate.Value);
                    }

                    if (!string.IsNullOrEmpty(co.Criteria!.CreatedByUserId))
                    {
                        query = query.Where(r => r.CreatedByUserId == co.Criteria!.CreatedByUserId);
                    }
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(r => r.CreatedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<GenericDataEntryRecordEntity>
                {
                    Data = items,
                    Count = totalCount,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Generic data entry record'ları listeleme hatası");
                throw;
            }
        }

        public async Task<PagedListDto<GenericDataEntryRecordEntity>> GetRecordsByDefinitionAsync(string definitionId, PagedListCo<GenericDataEntryFilterCo> co)
        {
            try
            {
                var query = _context.GenericDataEntryRecords
                    .Where(r => r.DefinitionId == definitionId);

                // Additional filters
                if (co.Criteria != null)
                {
                    if (!string.IsNullOrEmpty(co.Criteria!.Status))
                    {
                        query = query.Where(r => r.Status == co.Criteria!.Status);
                    }

                    if (co.Criteria!.StartDate.HasValue)
                    {
                        query = query.Where(r => r.CreatedAt >= co.Criteria!.StartDate.Value);
                    }

                    if (co.Criteria!.EndDate.HasValue)
                    {
                        query = query.Where(r => r.CreatedAt <= co.Criteria!.EndDate.Value);
                    }

                    if (!string.IsNullOrEmpty(co.Criteria!.CreatedByUserId))
                    {
                        query = query.Where(r => r.CreatedByUserId == co.Criteria!.CreatedByUserId);
                    }
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(r => r.CreatedAt)
                    .Skip((co.Pager.Page - 1) * co.Pager.Size)
                    .Take(co.Pager.Size)
                    .ToListAsync();

                return new PagedListDto<GenericDataEntryRecordEntity>
                {
                    Data = items,
                    Count = totalCount,
                    TotalCount = totalCount,
                    Page = co.Pager.Page,
                    Size = co.Pager.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition için record'ları listeleme hatası: {DefinitionId}", definitionId);
                throw;
            }
        }

        #endregion

        #region Statistics and Analytics

        public async Task<int> GetRecordCountByDefinitionAsync(string definitionId)
        {
            try
            {
                return await _context.GenericDataEntryRecords
                    .CountAsync(r => r.DefinitionId == definitionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition için record sayısı hatası: {DefinitionId}", definitionId);
                throw;
            }
        }

        public async Task<int> GetTotalDefinitionCountAsync()
        {
            try
            {
                return await _context.GenericDataEntryDefinitions
                    .CountAsync(d => d.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Toplam definition sayısı hatası");
                throw;
            }
        }

        public async Task<int> GetTotalRecordCountAsync()
        {
            try
            {
                return await _context.GenericDataEntryRecords.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Toplam record sayısı hatası");
                throw;
            }
        }

        public async Task<List<GenericDataEntryRecordEntity>> GetRecentRecordsAsync(int count = 10)
        {
            try
            {
                return await _context.GenericDataEntryRecords
                    .OrderByDescending(r => r.CreatedAt)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Son record'ları getirme hatası");
                throw;
            }
        }

        #endregion

        #region Validation Helpers

        public async Task<bool> IsDefinitionNameUniqueAsync(string name, string? excludeId = null)
        {
            try
            {
                var query = _context.GenericDataEntryDefinitions
                    .Where(d => d.Name == name && d.IsActive);

                if (!string.IsNullOrEmpty(excludeId))
                {
                    query = query.Where(d => d.Id != excludeId);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition name benzersizlik kontrolü hatası: {Name}", name);
                throw;
            }
        }

        public async Task<bool> IsDefinitionInUseAsync(string definitionId)
        {
            try
            {
                return await _context.GenericDataEntryRecords
                    .AnyAsync(r => r.DefinitionId == definitionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Definition kullanım kontrolü hatası: {DefinitionId}", definitionId);
                throw;
            }
        }

        #endregion
    }
}
